import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../models/customer.dart';

import '../models/invoice.dart';
import '../cubits/invoices_cubit.dart';
import '../l10n/app_localizations.dart';

import '../widgets/dialogs/invoice_payment_dialog.dart';

class InvoicePreviewPage extends StatefulWidget {
  final Customer customer;
  final List<InvoiceItem> selectedItems;
  final DateTime date;

  const InvoicePreviewPage({
    super.key,
    required this.customer,
    required this.selectedItems,
    required this.date,
  });

  @override
  State<InvoicePreviewPage> createState() => _InvoicePreviewPageState();
}

class _InvoicePreviewPageState extends State<InvoicePreviewPage> {
  late List<InvoiceItem> items;
  double paidAmount = 0.0;
  double collectedAmount = 0.0;
  final _collectedAmountController = TextEditingController();

  @override
  void initState() {
    super.initState();
    items = List.from(widget.selectedItems);
    // Set collected amount equal to total amount by default
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        collectedAmount = totalAmount;
        _collectedAmountController.text = totalAmount.toStringAsFixed(2);
      });
    });
  }

  @override
  void dispose() {
    _collectedAmountController.dispose();
    super.dispose();
  }

  double get totalAmount {
    return items.fold(0.0, (sum, item) => sum + item.totalPrice);
  }

  double get remainingBalance {
    return totalAmount - paidAmount;
  }

  void _editItemQuantity(int index, int newQuantity) {
    // Ensure minimum quantity of 1 - users can only edit quantities, not remove products
    if (newQuantity < 1) {
      newQuantity = 1;
    }

    final item = items[index];

    // Check if new quantity exceeds available stock
    if (newQuantity > item.product.quantity) {
      // Show error message and don't allow the change
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '${AppLocalizations.of(context).insufficientStock} ${item.product.category}. ${AppLocalizations.of(context).availableStock}: ${item.product.quantity}',
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
      return;
    }

    setState(() {
      items[index] = InvoiceItem(
        product: item.product,
        quantity: newQuantity,
        totalPrice: item.product.pricePerUnit * newQuantity,
      );
      // Update collected amount to match new total
      collectedAmount = totalAmount;
      _collectedAmountController.text = totalAmount.toStringAsFixed(2);
    });
  }

  void _addPayment() {
    showDialog(
      context: context,
      builder: (context) => InvoicePaymentDialog(
        totalAmount: totalAmount,
        currentPaidAmount: paidAmount,
        onPaymentAdded: (amount) {
          setState(() {
            paidAmount += amount;
          });
        },
      ),
    );
  }

  void _finalizeInvoice() async {
    if (items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).selectProducts),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Generate sequential invoice number
    final invoicesCubit = context.read<InvoicesCubit>();
    final invoiceNumber = invoicesCubit.generateNextInvoiceNumber();

    final invoice = Invoice(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      customer: widget.customer,
      items: items,
      date: widget.date,
      totalAmount: totalAmount,
      paidAmount: paidAmount > 0 ? paidAmount : null,
      isPaid: paidAmount >= totalAmount,
      collectedAmount: collectedAmount,
      invoiceNumber: invoiceNumber,
    );

    try {
      await context.read<InvoicesCubit>().addInvoice(invoice);

      if (mounted) {
        Navigator.of(context).pop(); // Go back to invoices page
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text(AppLocalizations.of(context).invoiceCreatedSuccessfully),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.invoicePreview),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: Colors.white,
        // Removed add product action - users can only edit quantities
      ),
      body: Column(
        children: [
          // Customer and Date Info
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(25),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.person,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      localizations.customer,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  widget.customer.name,
                  style: theme.textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      localizations.date,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  DateFormat('yyyy/MM/dd').format(widget.date),
                  style: theme.textTheme.titleMedium,
                ),
              ],
            ),
          ),

          // Products List
          Expanded(
            child: items.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.shopping_cart_outlined,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          localizations.selectProducts,
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: items.length,
                    itemBuilder: (context, index) {
                      final item = items[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ListTile(
                          leading: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primary.withAlpha(25),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.inventory_2,
                              color: theme.colorScheme.primary,
                            ),
                          ),
                          title: Text(item.product.category),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${localizations.quantity}: ${item.quantity} | ${localizations.total}: ${item.totalPrice.toStringAsFixed(2)} SAR',
                              ),
                              Text(
                                '${localizations.availableStock}: ${item.product.quantity}',
                                style: TextStyle(
                                  color: item.product.quantity < 5
                                      ? Colors.red
                                      : Colors.grey[600],
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                          trailing: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primary.withAlpha(25),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.remove),
                                  onPressed: item.quantity > 1
                                      ? () => _editItemQuantity(
                                          index, item.quantity - 1)
                                      : null, // Disable when quantity is 1
                                  iconSize: 20,
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        '${item.quantity}',
                                        style: theme.textTheme.titleMedium
                                            ?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          color: item.quantity ==
                                                  item.product.quantity
                                              ? Colors.red
                                              : null,
                                        ),
                                      ),
                                      if (item.quantity ==
                                          item.product.quantity)
                                        const Text(
                                          'MAX',
                                          style: TextStyle(
                                            fontSize: 8,
                                            color: Colors.red,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.add),
                                  onPressed:
                                      item.quantity < item.product.quantity
                                          ? () => _editItemQuantity(
                                              index, item.quantity + 1)
                                          : null, // Disable when at stock limit
                                  iconSize: 20,
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
          ),

          // Summary and Actions
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Total Amount
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      localizations.totalAmount,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${totalAmount.toStringAsFixed(2)} SAR',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),

                if (paidAmount > 0) ...[
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        localizations.paidAmount,
                        style: theme.textTheme.bodyMedium,
                      ),
                      Text(
                        '${paidAmount.toStringAsFixed(2)} SAR',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        localizations.remainingBalance,
                        style: theme.textTheme.bodyMedium,
                      ),
                      Text(
                        '${remainingBalance.toStringAsFixed(2)} SAR',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color:
                              remainingBalance > 0 ? Colors.red : Colors.green,
                        ),
                      ),
                    ],
                  ),
                ],

                const SizedBox(height: 16),

                // Collected Amount Input
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        localizations.collectedAmount,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _collectedAmountController,
                        keyboardType: const TextInputType.numberWithOptions(
                            decimal: true),
                        decoration: InputDecoration(
                          hintText: '0.00',
                          suffixText: 'SAR',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                        ),
                        onChanged: (value) {
                          setState(() {
                            collectedAmount = double.tryParse(value) ?? 0.0;
                          });
                        },
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: items.isNotEmpty ? _finalizeInvoice : null,
                        icon: const Icon(Icons.check),
                        label: Text(localizations.finalizeInvoice),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
