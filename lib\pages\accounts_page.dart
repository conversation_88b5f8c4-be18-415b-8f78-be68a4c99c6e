import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../cubits/customers_cubit.dart';
import '../cubits/suppliers_cubit.dart';
import '../cubits/payment_records_cubit.dart';
import '../models/customer.dart';
import '../models/supplier.dart';
import '../l10n/app_localizations.dart';
import '../widgets/dialogs/customer_payment_dialog.dart';
import '../widgets/dialogs/supplier_payment_dialog.dart';
import '../widgets/dialogs/add_customer_dialog.dart';
import '../widgets/dialogs/add_supplier_dialog.dart';
import '../constants/app_colors.dart';

class AccountsPage extends StatefulWidget {
  const AccountsPage({super.key});

  @override
  State<AccountsPage> createState() => _AccountsPageState();
}

class _AccountsPageState extends State<AccountsPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).accountsTab,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.primaryBlue,
        elevation: 0,
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'العملاء'),
            Tab(text: 'الموردين'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildCustomersTab(context),
          _buildSuppliersTab(context),
        ],
      ),
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          // Show FAB on customers tab (index 0) and suppliers tab (index 1)
          if (_tabController.index == 0) {
            return FloatingActionButton.extended(
              onPressed: () => showAddCustomerDialog(context),
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
              icon: const Icon(Icons.person_add),
              label: const Text(
                'إضافة عميل',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            );
          } else if (_tabController.index == 1) {
            return FloatingActionButton.extended(
              onPressed: () => showAddSupplierDialog(context: context),
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
              icon: const Icon(Icons.business_center),
              label: const Text(
                'إضافة مورد',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildCustomersTab(BuildContext context) {
    return BlocBuilder<CustomersCubit, CustomersState>(
      builder: (context, state) {
        if (state.customers.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.person,
                  size: 64,
                  color: AppColors.neutralGrey,
                ),
                SizedBox(height: 16),
                Text(
                  'لا يوجد عملاء',
                  style: TextStyle(
                    fontSize: 18,
                    color: AppColors.neutralGrey,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: state.customers.length,
          itemBuilder: (context, index) {
            final customer = state.customers[index];
            return Card(
              elevation: 2,
              margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 2),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      Color(0xFFFAFAFA),
                    ],
                  ),
                ),
                child: ListTile(
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  leading: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.backgroundBlue,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.person,
                      color: AppColors.primaryBlue,
                      size: 24,
                    ),
                  ),
                  title: Text(
                    customer.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: AppColors.darkGrey,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 6),
                      Row(
                        children: [
                          const Icon(
                            Icons.phone,
                            size: 14,
                            color: AppColors.primaryBlue,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            customer.whatsappNumber,
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppColors.primaryBlue,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            customer.remainingBalance < 0
                                ? Icons.account_balance
                                : Icons.account_balance_wallet,
                            size: 14,
                            color: customer.remainingBalance < 0
                                ? AppColors.successGreen
                                : AppColors.warningOrange,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            customer.remainingBalance < 0
                                ? 'رصيد العميل: SAR ${(-customer.remainingBalance).toStringAsFixed(2)}'
                                : '${AppLocalizations.of(context).remainingBalance}: SAR ${customer.remainingBalance.toStringAsFixed(2)}',
                            style: TextStyle(
                              color: customer.remainingBalance < 0
                                  ? AppColors.successGreen
                                  : AppColors.warningOrange,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(
                            Icons.payment,
                            size: 14,
                            color: AppColors.successGreen,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${AppLocalizations.of(context).paymentVoucher}: ${customer.totalPaid.toStringAsFixed(2)} SAR',
                            style: const TextStyle(
                              color: AppColors.successGreen,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                    ],
                  ),
                  trailing: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.backgroundBlue,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.chevron_right,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                  onTap: () => _showCustomerDetails(context, customer),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildSuppliersTab(BuildContext context) {
    return BlocBuilder<SuppliersCubit, SuppliersState>(
      builder: (context, state) {
        if (state.suppliers.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.business,
                  size: 64,
                  color: AppColors.neutralGrey,
                ),
                SizedBox(height: 16),
                Text(
                  'لا يوجد موردين',
                  style: TextStyle(
                    fontSize: 18,
                    color: AppColors.neutralGrey,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: state.suppliers.length,
          itemBuilder: (context, index) {
            final supplier = state.suppliers[index];
            return Card(
              elevation: 2,
              margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 2),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      Color(0xFFFAFAFA),
                    ],
                  ),
                ),
                child: ListTile(
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  leading: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.backgroundBlue,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.business,
                      color: AppColors.primaryBlue,
                      size: 24,
                    ),
                  ),
                  title: Text(
                    supplier.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: AppColors.darkGrey,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 6),
                      Row(
                        children: [
                          const Icon(
                            Icons.phone,
                            size: 14,
                            color: AppColors.primaryBlue,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            supplier.phoneNumber,
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppColors.primaryBlue,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(
                            Icons.account_balance_wallet,
                            size: 14,
                            color: AppColors.warningOrange,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'المبلغ المستحق: ${supplier.remainingBalance.toStringAsFixed(2)} ريال',
                            style: const TextStyle(
                              color: AppColors.warningOrange,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(
                            Icons.payment,
                            size: 14,
                            color: AppColors.successGreen,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'إجمالي المدفوع: ${supplier.totalPaid.toStringAsFixed(2)} ريال',
                            style: const TextStyle(
                              color: AppColors.successGreen,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  trailing: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.backgroundBlue,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.chevron_right,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                  onTap: () => _showSupplierDetails(context, supplier),
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _showSupplierDetails(BuildContext context, Supplier supplier) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Supplier name
              Text(
                supplier.name,
                style: const TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryBlue,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),

              // Phone number
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'رقم الهاتف: ${supplier.phoneNumber}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: AppColors.neutralGrey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Financial information
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'المبلغ المستحق:',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.neutralGrey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      '${supplier.remainingBalance.toStringAsFixed(2)} ريال',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.warningOrange,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Total Paid
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'إجمالي المدفوع:',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.neutralGrey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      '${supplier.totalPaid.toStringAsFixed(2)} ريال',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.successGreen,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Delete button
                  TextButton(
                    onPressed: () => _confirmDeleteSupplier(context, supplier),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.errorRed,
                    ),
                    child: const Text('حذف'),
                  ),

                  // Pay button (only show if there's a remaining balance)
                  if (supplier.remainingBalance > 0)
                    ElevatedButton(
                      onPressed: () {
                        // Close the details dialog first
                        Navigator.pop(context);
                        // Show the payment dialog
                        showSupplierPaymentDialog(
                          context: context,
                          supplier: supplier,
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryBlue,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('دفع'),
                    ),

                  // Cancel button
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.neutralGrey,
                    ),
                    child: const Text('إلغاء'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showCustomerDetails(BuildContext context, Customer customer) {
    final localizations = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white, // White background
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Customer name
              Text(
                customer.name,
                style: const TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),

              // WhatsApp number
              Row(
                children: [
                  Expanded(
                    child: Text(
                      '${localizations.whatsappNumber}: ${customer.whatsappNumber}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: AppColors.neutralGrey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Financial information
              Row(
                children: [
                  Expanded(
                    child: Text(
                      customer.remainingBalance < 0
                          ? 'رصيد العميل:'
                          : '${localizations.remainingBalance}:',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.neutralGrey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      customer.remainingBalance < 0
                          ? 'SAR ${(-customer.remainingBalance).toStringAsFixed(2)}'
                          : 'SAR ${customer.remainingBalance.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: customer.remainingBalance < 0
                            ? AppColors.successGreen
                            : AppColors.warningOrange,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Payment Voucher (Total Paid)
              Row(
                children: [
                  Expanded(
                    child: Text(
                      '${localizations.paymentVoucher}:',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.neutralGrey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'SAR ${customer.totalPaid.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.successGreen,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Action buttons
              Wrap(
                alignment: WrapAlignment.spaceEvenly,
                spacing: 8,
                runSpacing: 8,
                children: [
                  // Delete button
                  TextButton(
                    onPressed: () => _confirmDeleteCustomer(context, customer),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.errorRed,
                    ),
                    child: Text(localizations.delete),
                  ),

                  // Payment Voucher button (always show)

                  // Pay button (always show, but change text based on balance)
                  ElevatedButton.icon(
                    onPressed: () {
                      // Close the details dialog first
                      Navigator.pop(context);
                      // Show the payment dialog
                      showCustomerPaymentDialog(
                        context: context,
                        customer: customer,
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: customer.remainingBalance < 0
                          ? AppColors.accentTeal
                          : AppColors.primaryBlue,
                      foregroundColor: Colors.white,
                    ),
                    icon: Icon(
                      customer.remainingBalance < 0
                          ? Icons.add_card
                          : Icons.payment,
                      size: 16,
                    ),
                    label: Text(
                      customer.remainingBalance < 0
                          ? 'إضافة رصيد'
                          : localizations.pay,
                    ),
                  ),

                  // Cancel button
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.neutralGrey,
                    ),
                    child: Text(localizations.cancel),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _confirmDeleteSupplier(BuildContext context, Supplier supplier) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.errorRed.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.warning_amber,
                color: AppColors.errorRed,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'تأكيد الحذف',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.errorRed,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'هل أنت متأكد من حذف هذا المورد؟',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.darkGrey,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.backgroundBlue.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.primaryBlue.withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المورد: ${supplier.name}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'الهاتف: ${supplier.phoneNumber}',
                    style: const TextStyle(color: AppColors.darkGrey),
                  ),
                  if (supplier.remainingBalance > 0)
                    Text(
                      'المبلغ المستحق: ${supplier.remainingBalance.toStringAsFixed(2)} ريال',
                      style: const TextStyle(
                        color: AppColors.warningOrange,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                ],
              ),
            ),
            if (supplier.remainingBalance > 0) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.warningOrange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: AppColors.warningOrange.withValues(alpha: 0.3),
                  ),
                ),
                child: const Row(
                  children: [
                    Icon(
                      Icons.warning,
                      color: AppColors.warningOrange,
                      size: 16,
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'تحذير: يوجد مبلغ مستحق لهذا المورد',
                        style: TextStyle(
                          color: AppColors.warningOrange,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.neutralGrey,
            ),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context); // Close confirmation dialog
              Navigator.pop(context); // Close details dialog
              context.read<SuppliersCubit>().deleteSupplier(supplier);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم حذف المورد بنجاح'),
                  backgroundColor: AppColors.successGreen,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorRed,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _confirmDeleteCustomer(BuildContext context, Customer customer) {
    final localizations = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.errorRed.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.warning_amber,
                color: AppColors.errorRed,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                localizations.delete,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.errorRed,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'هل أنت متأكد من حذف هذا العميل؟',
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.darkGrey,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.backgroundBlue.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.primaryBlue.withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${localizations.customer}: ${customer.name}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${localizations.whatsappNumber}: ${customer.whatsappNumber}',
                    style: const TextStyle(color: AppColors.darkGrey),
                  ),
                  if (customer.remainingBalance > 0)
                    Text(
                      '${localizations.remainingBalance}: ${customer.remainingBalance.toStringAsFixed(2)} SAR',
                      style: const TextStyle(
                        color: AppColors.warningOrange,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                ],
              ),
            ),
            if (customer.remainingBalance > 0) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.warningOrange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: AppColors.warningOrange.withValues(alpha: 0.3),
                  ),
                ),
                child: const Row(
                  children: [
                    Icon(
                      Icons.warning,
                      color: AppColors.warningOrange,
                      size: 16,
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'تحذير: يوجد مبلغ مستحق على هذا العميل',
                        style: TextStyle(
                          color: AppColors.warningOrange,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.neutralGrey,
            ),
            child: Text(localizations.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context); // Close confirmation dialog
              Navigator.pop(context); // Close details dialog
              context.read<CustomersCubit>().deleteCustomer(customer);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف العميل بنجاح'),
                  backgroundColor: AppColors.successGreen,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorRed,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(localizations.delete),
          ),
        ],
      ),
    );
  }
}
