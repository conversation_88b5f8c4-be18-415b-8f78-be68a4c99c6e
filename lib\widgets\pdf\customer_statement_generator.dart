import 'package:alzobidi/widgets/invoices/pdf_invoice_components_2.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:flutter/material.dart';
import '../../models/invoice.dart';
import '../../models/customer.dart';
import '../../models/payment_record.dart';
import '../../l10n/app_localizations.dart';

// Transaction class to represent different types of customer transactions
class CustomerTransaction {
  final DateTime date;
  final String description;
  final String referenceNumber;
  final double debitAmount; // له (amount owed to customer)
  final double creditAmount; // عليه (amount customer owes)
  final String type; // 'invoice', 'payment', 'return'

  CustomerTransaction({
    required this.date,
    required this.description,
    required this.referenceNumber,
    required this.debitAmount,
    required this.creditAmount,
    required this.type,
  });
}

class CustomerStatementGenerator {
  static Future<void> generateAndShareStatement(
    BuildContext context,
    Customer customer,
    List<Invoice> invoices,
    List<PaymentRecord> paymentRecords,
  ) async {
    final localizations = AppLocalizations.of(context);
    final pdf = pw.Document();

    // Load the Arabic font
    final arabicFont =
        await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
    final ttf = pw.Font.ttf(arabicFont);

    // Load the logo image
    final logoImage = await rootBundle.load('assets/logo.jpg');
    final logoImageData = logoImage.buffer.asUint8List();
    final logo = pw.MemoryImage(logoImageData);

    // Load the QR code image
    final qrImage = await rootBundle.load('assets/qr.jpg');
    final qrImageData = qrImage.buffer.asUint8List();
    final qrCode = pw.MemoryImage(qrImageData);

    // Create a theme with the Arabic font
    final theme = pw.ThemeData.withFont(
      base: ttf,
      bold: ttf,
      italic: ttf,
      boldItalic: ttf,
    );

    // Create transactions list from invoices and payment records
    final transactions = <CustomerTransaction>[];

    // Add invoice transactions
    for (final invoice in invoices) {
      final invoiceDisplayNumber = invoice.invoiceNumber ??
          (invoice.id.length > 8 ? invoice.id.substring(0, 8) : invoice.id);

      transactions.add(CustomerTransaction(
        date: invoice.date,
        description: 'فاتورة مبيعات رقم $invoiceDisplayNumber',
        referenceNumber: invoiceDisplayNumber,
        debitAmount: 0.0,
        creditAmount: invoice.totalAmount,
        type: 'invoice',
      ));

      // Add return transactions if any
      if (invoice.totalReturnedAmount > 0) {
        transactions.add(CustomerTransaction(
          date: invoice.lastReturnDate ?? invoice.date,
          description: 'مرتجعات فاتورة رقم $invoiceDisplayNumber',
          referenceNumber: invoiceDisplayNumber,
          debitAmount: invoice.totalReturnedAmount,
          creditAmount: 0.0,
          type: 'return',
        ));
      }
    }

    // Add payment transactions
    for (final payment in paymentRecords) {
      if (payment.invoiceId == 'EXCESS_PAYMENT') {
        transactions.add(CustomerTransaction(
          date: payment.timestamp,
          description: 'رصيد اضافي',
          referenceNumber: '-',
          debitAmount: payment.amount,
          creditAmount: 0.0,
          type: 'payment',
        ));
      } else {
        // Find the corresponding invoice to get the display number
        final correspondingInvoice = invoices.firstWhere(
          (invoice) => invoice.id == payment.invoiceId,
          orElse: () => Invoice(
            id: payment.invoiceId,
            customer: customer,
            items: [],
            date: payment.timestamp,
            totalAmount: 0.0,
          ),
        );
        final paymentInvoiceDisplayNumber =
            correspondingInvoice.invoiceNumber ??
                (payment.invoiceId.length > 8
                    ? payment.invoiceId.substring(0, 8)
                    : payment.invoiceId);

        // Add payment transaction
        if (payment.amount > 0) {
          transactions.add(CustomerTransaction(
            date: payment.timestamp,
            description:
                'سند قبض (دفعة على فاتورة $paymentInvoiceDisplayNumber)',
            referenceNumber: paymentInvoiceDisplayNumber,
            debitAmount: payment.amount,
            creditAmount: 0.0,
            type: 'payment',
          ));
        }

        // Add collected amount transaction if it exists
        if (payment.collectedAmount > 0) {
          transactions.add(CustomerTransaction(
            date: payment.timestamp,
            description: 'مبلغ محصل (فاتورة $paymentInvoiceDisplayNumber)',
            referenceNumber: paymentInvoiceDisplayNumber,
            debitAmount: payment.collectedAmount,
            creditAmount: 0.0,
            type: 'collected',
          ));
        }
      }
    }

    // Sort transactions by date (oldest first)
    transactions.sort((a, b) => a.date.compareTo(b.date));

    // Calculate date range from actual transactions
    DateTime? fromDate;
    DateTime? toDate;

    if (transactions.isNotEmpty) {
      fromDate = transactions.first.date; // تاريخ أول حدث
      toDate = transactions.last.date; // تاريخ آخر حدث
    }

    // Calculate running balances and summary
    double runningBalance = 0.0;
    final transactionsWithBalance = <Map<String, dynamic>>[];

    for (final transaction in transactions) {
      runningBalance += transaction.creditAmount - transaction.debitAmount;
      transactionsWithBalance.add({
        'transaction': transaction,
        'balance': runningBalance,
      });
    }

    // Calculate summary totals
    final totalCredit =
        transactions.fold<double>(0.0, (sum, t) => sum + t.creditAmount);
    final totalDebit =
        transactions.fold<double>(0.0, (sum, t) => sum + t.debitAmount);
    final finalBalance = totalCredit - totalDebit;

    // Add page
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: theme,
        margin: const pw.EdgeInsets.all(20),
        maxPages: 100,
        build: (context) => [
          _buildNewStatementHeader(ttf, logo, localizations),
          pw.SizedBox(height: 20),
          _buildNewCustomerInfoHeader(
              customer, ttf, localizations, logo, fromDate, toDate),
          pw.SizedBox(height: 20),
          _buildNewTransactionsTable(
              transactionsWithBalance, ttf, localizations),
          pw.SizedBox(height: 20),
          _buildNewSummarySection(
              totalCredit, totalDebit, finalBalance, ttf, localizations),
          // Add space before footer to push it to bottom
          pw.SizedBox(height: 40),
          // Footer only appears on the last page
          buildFooter(ttf, qrCode),
        ],
      ),
    );

    // Save and share the PDF
    final output = await getTemporaryDirectory();
    final file = File('${output.path}/customer_statement_${customer.name}.pdf');
    await file.writeAsBytes(await pdf.save());

    await Share.shareXFiles(
      [XFile(file.path)],
      text: '${localizations.customerSummary} - ${customer.name}',
    );
  }

  // New header method matching the image design

  static pw.Widget _buildNewStatementHeader(
      pw.Font ttf, pw.MemoryImage logo, AppLocalizations localizations) {
    return pw.Stack(
      alignment: pw.Alignment.topCenter,
      children: [
        pw.Container(
          width: double.infinity,
          height: 30,
          color: PdfColor.fromHex('#125158'), // Dark gray
        ),
        pw.Container(
          width: 300,
          height: 50,
          color: PdfColor.fromHex('#1c666d'), // Dark gray
        ),
      ],
    );
  }

  // New customer info header matching the image design
  static pw.Widget _buildNewCustomerInfoHeader(
    Customer customer,
    pw.Font ttf,
    AppLocalizations localizations,
    logo,
    DateTime? fromDate,
    DateTime? toDate,
  ) {
    return pw.Column(
      children: [
        pw.SizedBox(height: 5),
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.center,
          children: [
            pw.SizedBox(
              height: 80,
              width: 80,
              child: pw.Image(logo, fit: pw.BoxFit.fill),
            ),
            pw.SizedBox(width: 5),
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              mainAxisAlignment: pw.MainAxisAlignment.start,
              children: [
                pw.Text(
                  'مؤسسه محمد علي بكري البيطريه',
                  style: pw.TextStyle(
                    font: ttf,
                    fontWeight: pw.FontWeight.bold,
                    fontSize: 18,
                    color: PdfColor.fromHex('#125158'),
                  ),
                ),
                pw.Text(
                  'س.ت 4031319572',
                  style: pw.TextStyle(
                    font: ttf,
                    fontSize: 15,
                    color: PdfColor.fromHex('#125158'), // Dark gray
                  ),
                ),
              ],
            ),
          ],
        ),
        pw.SizedBox(height: 10),
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Column(
              mainAxisAlignment: pw.MainAxisAlignment.center,
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Row(
                  children: [
                    pw.Text(
                      'اسم العميل: ',
                      style: pw.TextStyle(
                        font: ttf,
                        fontWeight: pw.FontWeight.bold,
                        fontSize: 14,
                        color: PdfColor.fromHex('#1c666d'), // Dark gray
                      ),
                    ),
                    pw.Text(
                      customer.name,
                      style: pw.TextStyle(
                        font: ttf,
                        fontSize: 13,
                        color: PdfColor.fromHex('#000000'), // Dark gray
                      ),
                    ),
                  ],
                ),
                pw.SizedBox(height: 4),
                pw.Row(children: [
                  pw.Text(
                    'من التاريخ:  ',
                    style: pw.TextStyle(
                      font: ttf,
                      fontSize: 14,
                      color: PdfColor.fromHex('#1c666d'),
                    ),
                  ),
                  pw.Text(
                    fromDate != null ? _formatDateShort(fromDate) : 'لا يوجد',
                    style: pw.TextStyle(
                      font: ttf,
                      fontSize: 13,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.black,
                    ),
                  ),
                ]),
                pw.SizedBox(height: 4),
                pw.Row(
                  children: [
                    pw.Text(
                      'إلى التاريخ:  ',
                      style: pw.TextStyle(
                        font: ttf,
                        fontSize: 14,
                        color: PdfColor.fromHex('#1c666d'),
                      ),
                    ),
                    pw.Text(
                      toDate != null ? _formatDateShort(toDate) : 'لا يوجد',
                      style: pw.TextStyle(
                        font: ttf,
                        fontSize: 13,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.black,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            pw.Text(
              ' كشف حساب عميل   ',
              style: pw.TextStyle(
                font: ttf,
                fontWeight: pw.FontWeight.bold,
                fontSize: 34,
                color: PdfColor.fromHex('#125158'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // New transactions table matching the image design
  static pw.Widget _buildNewTransactionsTable(
    List<Map<String, dynamic>> transactionsWithBalance,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Table(
      border: pw.TableBorder.all(
        color: PdfColors.grey400,
        width: 0.5,
      ),
      columnWidths: {
        5: const pw.FlexColumnWidth(2), // التاريخ
        4: const pw.FlexColumnWidth(4), // البيان
        3: const pw.FlexColumnWidth(2), // الرقم
        2: const pw.FlexColumnWidth(2), // له
        1: const pw.FlexColumnWidth(2), // عليه
        0: const pw.FlexColumnWidth(2), // الرصيد
      },
      children: [
        // Header row
        pw.TableRow(
          decoration: pw.BoxDecoration(
            color: PdfColor.fromHex('#D6B36A'),
          ),
          children: [
            _buildNewTableCell('الرصيد', ttf, isHeader: true),
            _buildNewTableCell('عليه', ttf, isHeader: true),
            _buildNewTableCell('له', ttf, isHeader: true),
            _buildNewTableCell('الفاتوره رقم', ttf, isHeader: true),
            _buildNewTableCell('البيان', ttf, isHeader: true),
            _buildNewTableCell('التاريخ', ttf, isHeader: true),
          ],
        ),
        // Data rows
        ...transactionsWithBalance.asMap().entries.map((entry) {
          final index = entry.key;
          final data = entry.value;
          final transaction = data['transaction'] as CustomerTransaction;
          final balance = data['balance'] as double;

          return pw.TableRow(
            decoration: pw.BoxDecoration(
              color: index % 2 == 0
                  ? PdfColors.white
                  : PdfColor.fromHex('#F9F9F9'),
            ),
            children: [
              _buildNewTableCell(
                balance.toStringAsFixed(0),
                ttf,
                color: balance > 0
                    ? PdfColor.fromHex('#F44336')
                    : PdfColor.fromHex('#4CAF50'),
              ),
              _buildNewTableCell(
                transaction.creditAmount > 0
                    ? transaction.creditAmount.toStringAsFixed(0)
                    : '-',
                ttf,
              ),
              _buildNewTableCell(
                transaction.debitAmount > 0
                    ? transaction.debitAmount.toStringAsFixed(0)
                    : '-',
                ttf,
              ),
              _buildNewTableCell(transaction.referenceNumber, ttf),
              _buildNewTableCell(transaction.description, ttf),
              _buildNewTableCell(_formatDateShort(transaction.date), ttf),
            ],
          );
        }).toList(),
      ],
    );
  }

  // New summary section matching the image design
  static pw.Widget _buildNewSummarySection(
    double totalCredit,
    double totalDebit,
    double finalBalance,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex('#F8F9FA'),
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(
          color: PdfColors.grey400,
          width: 1,
        ),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          _buildSummaryRow(
            'إجمالي الرصيد عليه بالريال السعودي:',
            totalCredit.toStringAsFixed(0),
            ttf,
          ),
          pw.SizedBox(height: 8),
          _buildSummaryRow(
            'إجمالي المدفوع من العميل بالريال السعودي:',
            totalDebit.toStringAsFixed(0),
            ttf,
          ),
          pw.SizedBox(height: 8),
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              color: PdfColor.fromHex('#D6B36A'),
              borderRadius: pw.BorderRadius.circular(4),
            ),
            child: _buildSummaryRow(
              'الرصيد الحالي بالريال السعودي:',
              finalBalance.toStringAsFixed(0),
              ttf,
              isHighlighted: true,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method for summary rows
  static pw.Widget _buildSummaryRow(
    String label,
    String value,
    pw.Font ttf, {
    bool isHighlighted = false,
  }) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Text(
          label,
          style: pw.TextStyle(
            font: ttf,
            fontSize: 12,
            color: isHighlighted ? PdfColors.white : PdfColors.grey800,
          ),
        ),
        pw.Text(
          value,
          style: pw.TextStyle(
            font: ttf,
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
            color: isHighlighted ? PdfColors.white : PdfColors.grey800,
          ),
        ),
      ],
    );
  }

  // New table cell method
  static pw.Widget _buildNewTableCell(
    String text,
    pw.Font ttf, {
    bool isHeader = false,
    PdfColor? color,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: ttf,
          fontSize: isHeader ? 12 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          color: isHeader ? PdfColors.white : (color ?? PdfColors.grey800),
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  // Footer section with company info and QR code
  static pw.Widget _buildFooter(
    pw.Font ttf,
    pw.MemoryImage qrCode,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          colors: [
            PdfColor.fromHex('#125158'),
            PdfColor.fromHex('#1c666d'),
          ],
        ),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          // Company info
          pw.Expanded(
            flex: 2,
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              mainAxisSize: pw.MainAxisSize.min,
              children: [
                pw.Text(
                  'مؤسسة محمد علي بكري البيطرية',
                  style: pw.TextStyle(
                    font: ttf,
                    fontSize: 12,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.white,
                  ),
                ),
                pw.SizedBox(height: 2),
                pw.Text(
                  'الهاتف: +966 XX XXX XXXX',
                  style: pw.TextStyle(
                    font: ttf,
                    fontSize: 8,
                    color: PdfColors.white,
                  ),
                ),
              ],
            ),
          ),

          // QR Code
          pw.Container(
            width: 40,
            height: 40,
            child: pw.Image(qrCode, fit: pw.BoxFit.contain),
          ),

          // Thank you message
          pw.Expanded(
            flex: 2,
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              mainAxisSize: pw.MainAxisSize.min,
              children: [
                pw.Text(
                  'شكراً لثقتكم بنا',
                  style: pw.TextStyle(
                    font: ttf,
                    fontSize: 12,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.white,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
                pw.SizedBox(height: 2),
                pw.Text(
                  'تاريخ الطباعة: ${_formatDateShort(DateTime.now())}',
                  style: pw.TextStyle(
                    font: ttf,
                    fontSize: 8,
                    color: PdfColors.white,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Keep only the date formatting method
  static String _formatDateShort(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}-${date.month.toString().padLeft(2, '0')}-${date.year}';
  }
}
